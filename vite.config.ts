import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), vueDevTools()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 3000,
    // 代理配置
    proxy: {
      '/dwyztApp/dwyzt': {
        target: 'http://172.19.139.41:8087/mock/189/dwyzt',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, ''),
      },
    },
  },
})
